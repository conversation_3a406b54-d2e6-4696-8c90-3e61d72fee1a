part of xr_helper;

Flushbar showBarAlert(BuildContext context, String msg,
    {Color iconColor = Colors.white,
    IconData icon = Icons.check_circle_outline,
    bool isError = false,
    String? title,
    Color? color,
    int duration = 5,
    Function(Flushbar)? onTap}) {
  return Flushbar(
    backgroundColor: color ?? (isError ? Colors.red : const Color(0xFF4CAF50)),
    title: title,
    message: msg,
    icon: Icon(isError ? Icons.error : icon, size: 28.0, color: iconColor),
    duration: Duration(seconds: duration),
    flushbarStyle: FlushbarStyle.FLOATING,
    flushbarPosition: FlushbarPosition.TOP,
    margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
    padding: const EdgeInsets.all(16),
    borderRadius: BorderRadius.circular(10),
    onTap: onTap,
  )..show(context);
}

extension ShowBars on BuildContext {
  //? Alerts
  void showBarMessage(String msg,
          {bool isError = false,
          Function(Flushbar)? onTap,
          int duration = 5}) =>
      showBarAlert(this, msg,
          isError: isError, onTap: onTap, duration: duration);
}

FToast toastAlert = FToast();

void showToast(String msg,
    {Color iconColor = Colors.white,
    bool isError = false,
    IconData icon = Icons.check_circle_outline,
    String? title,
    Color? color,
    ToastGravity gravity = ToastGravity.TOP,
    Duration duration = const Duration(seconds: 2)}) {
  Widget toast = Container(
    padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 12.0),
    decoration: BoxDecoration(
      borderRadius: BorderRadius.circular(10.0),
      color: color ?? (isError ? Colors.red : const Color(0xFF4CAF50)),
    ),
    child: Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(isError ? Icons.error : icon, size: 28.0, color: iconColor),
        const SizedBox(width: 12.0),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (title != null)
                Text(title,
                    style: const TextStyle(
                        fontWeight: FontWeight.bold, color: Colors.white)),
              Text(msg, style: const TextStyle(color: Colors.white)),
            ],
          ),
        ),
      ],
    ),
  );

  toastAlert.showToast(
    child: toast,
    gravity: gravity,
    toastDuration: duration,
  );
}
