import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../core/shared/providers/network_api_service_provider.dart';
import '../controllers/leave_controller.dart';
import '../repositories/leave_repository.dart';

// * Leave Repo Provider ========================================
final leaveRepoProvider = Provider<LeaveRepository>((ref) {
  final networkApiService = ref.watch(networkServiceProvider);

  return LeaveRepository(networkApiService: networkApiService);
});

// * Leave Controller Provider ========================================
final leaveControllerProvider = Provider<LeaveController>(
  (ref) {
    final leaveRepo = ref.watch(leaveRepoProvider);

    return LeaveController(
      leaveRepo: leaveRepo,
    );
  },
);

final leaveControllerNotifierProvider = ChangeNotifierProvider<LeaveController>(
  (ref) {
    final leaveRepo = ref.watch(leaveRepoProvider);

    return LeaveController(
      leaveRepo: leaveRepo,
    );
  },
);

// * Get Leave List Future Provider ========================================
final getLeaveListFutureProvider = FutureProvider((ref) {
  final leaveRepo = ref.watch(leaveRepoProvider);

  return leaveRepo.getLeaveList();
});
