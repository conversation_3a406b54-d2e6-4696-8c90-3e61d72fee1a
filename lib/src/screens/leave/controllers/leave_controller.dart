import 'package:opti_tickets/src/screens/leave/models/leave_model.dart';
import 'package:opti_tickets/src/screens/leave/repositories/leave_repository.dart';
import 'package:xr_helper/xr_helper.dart';

class LeaveController extends BaseVM {
  final LeaveRepository leaveRepo;

  LeaveController({
    required this.leaveRepo,
  });

  // * Get Leave List
  Future<LeaveListModel> getLeaveList() async {
    try {
      return await leaveRepo.getLeaveList();
    } catch (e) {
      Log.e('Leave List Error: $e');
      return LeaveListModel.empty();
    }
    // return await baseFunction(
    //   () async {
    // },
    // );
  }

  // * Add Leave Request
  Future<bool> addLeaveRequest({
    required int leaveType,
    required String fromDate,
    required String toDate,
    required String leaveReason,
  }) async {
    return await baseFunction(
      () async {
        return await leaveRepo.addLeaveRequest(
          leaveType: leaveType,
          fromDate: fromDate,
          toDate: toDate,
          leaveReason: leaveReason,
        );
      },
    );
  }

  // * Update Leave Request
  Future<bool> updateLeaveRequest({
    required int requestId,
    required int leaveType,
    required String fromDate,
    required String toDate,
    required String leaveReason,
  }) async {
    return await baseFunction(
      () async {
        return await leaveRepo.updateLeaveRequest(
          requestId: requestId,
          leaveType: leaveType,
          fromDate: fromDate,
          toDate: toDate,
          leaveReason: leaveReason,
        );
      },
    );
  }

  // * Delete Leave Request
  Future<bool> deleteLeaveRequest({
    required int requestId,
  }) async {
    return await baseFunction(
      () async {
        return await leaveRepo.deleteLeaveRequest(
          requestId: requestId,
        );
      },
    );
  }
}
