import 'dart:developer';

import 'package:equatable/equatable.dart';

class LeaveListModel extends Equatable {
  final bool canTakeAction;
  final List<LeaveRequest> leaves;

  const LeaveListModel({
    this.canTakeAction = false,
    this.leaves = const [],
  });

  factory LeaveListModel.fromJson(Map<String, dynamic> json) {
    return LeaveListModel(
      canTakeAction: json['can_take_action'] ?? false,
      leaves: (json['dt'] as List<dynamic>?)
              ?.map((e) => LeaveRequest.fromJson(e))
              .toList() ??
          [],
    );
  }

  factory LeaveListModel.empty() => const LeaveListModel();

  @override
  List<Object?> get props => [canTakeAction, leaves];
}

class LeaveRequest extends Equatable {
  final int ulrId;
  final String ulrEmp;
  final String ulrIssuedAt;
  final String ulrLeaveType;
  final String ulrFromDate;
  final String ulrToDate;
  final String ulrReasonEmp;
  final LeaveStatus ulrStatus;

  const LeaveRequest({
    this.ulrId = 0,
    this.ulrEmp = '',
    this.ulrIssuedAt = '',
    this.ulrLeaveType = '',
    this.ulrFromDate = '',
    this.ulrToDate = '',
    this.ulrReasonEmp = '',
    this.ulrStatus = const LeaveStatus(),
  });

  factory LeaveRequest.fromJson(Map<String, dynamic> json) {
    log('aFFFF ${json}');

    return LeaveRequest(
      ulrId: json['ulr_id'] ?? 0,
      ulrEmp: json['ulr_emp'] ?? '',
      ulrIssuedAt: json['ulr_issued_at'] ?? '',
      ulrLeaveType: json['ulr_leaveType'] ?? '',
      ulrFromDate: json['ulr_fromDate'] ?? '',
      ulrToDate: json['ulr_toDate'] ?? '',
      ulrReasonEmp: json['ulr_reasonEmp'] ?? '',
      ulrStatus: json['ulr_status'] is String
          ? LeaveStatus.fromJson(const {'status': 'pending'})
          : LeaveStatus.fromJson(json['ulr_status'] ?? {}),
    );
  }

  @override
  List<Object?> get props => [
        ulrId,
        ulrEmp,
        ulrIssuedAt,
        ulrLeaveType,
        ulrFromDate,
        ulrToDate,
        ulrReasonEmp,
        ulrStatus,
      ];
}

class LeaveStatus extends Equatable {
  final String status;
  final String statusNameE;
  final String statusNameA;
  final String statusColor;
  final String date;
  final String time;
  final String? message;
  final int? approvedBy;

  const LeaveStatus({
    this.status = '',
    this.statusNameE = '',
    this.statusNameA = '',
    this.statusColor = '',
    this.date = '',
    this.time = '',
    this.message,
    this.approvedBy,
  });

  factory LeaveStatus.fromJson(Map<String, dynamic> json) {
    return LeaveStatus(
      status: json['status'] ?? '',
      statusNameE: json['status_nameE'] ?? '',
      statusNameA: json['status_nameA'] ?? '',
      statusColor: json['status_color'] ?? '',
      date: json['date'] ?? '',
      time: json['time'] ?? '',
      message: json['message'],
      approvedBy: json['approved_by'],
    );
  }

  @override
  List<Object?> get props => [
        status,
        statusNameE,
        statusNameA,
        statusColor,
        date,
        time,
        message,
        approvedBy,
      ];
}

class LeaveType extends Equatable {
  final int id;
  final String nameEn;
  final String nameAr;

  const LeaveType({
    this.id = 0,
    this.nameEn = '',
    this.nameAr = '',
  });

  @override
  List<Object?> get props => [id, nameEn, nameAr];
}

// Common leave types
const List<LeaveType> leaveTypes = [
  LeaveType(id: 1, nameEn: 'Vacation Leave', nameAr: 'إجازة اعتيادية'),
  LeaveType(id: 2, nameEn: 'Sick Leave', nameAr: 'إجازة مرضية'),
  LeaveType(id: 3, nameEn: 'Request Leave', nameAr: 'إجازة مطلوبة'),
];
