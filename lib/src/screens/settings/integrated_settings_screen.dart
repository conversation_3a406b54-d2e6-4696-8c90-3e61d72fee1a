import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:image_picker/image_picker.dart';
import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
import 'package:opti_tickets/src/core/shared/services/app_settings/controller/settings_controller.dart';
import 'package:opti_tickets/src/core/theme/color_manager.dart';
import 'package:opti_tickets/src/screens/main_screen/view/main_screen.dart';
import 'package:opti_tickets/src/screens/profile/models/profile_model.dart';
import 'package:opti_tickets/src/screens/profile/providers/profile_providers.dart';
import 'package:opti_tickets/src/screens/settings/widgets/setting_card.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../core/shared/widgets/loading/loading_widget.dart';
import '../home/<USER>/home_providers.dart';
import '../home/<USER>/widgets/home_app_bar_widget.dart';

class IntegratedSettingsScreen extends ConsumerStatefulWidget {
  const IntegratedSettingsScreen({super.key});

  @override
  ConsumerState<IntegratedSettingsScreen> createState() =>
      _IntegratedSettingsScreenState();
}

class _IntegratedSettingsScreenState
    extends ConsumerState<IntegratedSettingsScreen> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _mobileController;
  String? _selectedLanguage;
  File? _selectedImage;
  final ImagePicker _picker = ImagePicker();
  bool _isEditing = false;

  @override
  void initState() {
    super.initState();
    _mobileController = TextEditingController();
  }

  @override
  void dispose() {
    _mobileController.dispose();
    super.dispose();
  }

  Future<void> _pickImage() async {
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return SafeArea(
          child: Wrap(
            children: [
              ListTile(
                leading: const Icon(Icons.photo_library),
                title: Text(context.tr.gallery),
                onTap: () async {
                  Navigator.pop(context);
                  final XFile? image = await _picker.pickImage(
                    source: ImageSource.gallery,
                    imageQuality: 80,
                  );
                  if (image != null) {
                    setState(() {
                      _selectedImage = File(image.path);
                    });
                  }
                },
              ),
              ListTile(
                leading: const Icon(Icons.photo_camera),
                title: Text(context.tr.camera),
                onTap: () async {
                  Navigator.pop(context);
                  final XFile? image = await _picker.pickImage(
                    source: ImageSource.camera,
                    imageQuality: 80,
                  );
                  if (image != null) {
                    setState(() {
                      _selectedImage = File(image.path);
                    });
                  }
                },
              ),
            ],
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final settingsController = ref.watch(settingsControllerProvider);
    final profileController = ref.watch(profileControllerNotifierProvider);
    final profileFuture = ref.watch(getProfileFutureProvider);

    Future<void> _updateProfile(ProfileEmployee employee) async {
      if (!_formKey.currentState!.validate()) return;

      // final profileController = ref.read(profileControllerProvider);
      try {
        await profileController.updateProfile(
          mobile: _mobileController.text,
          defaultLang: _selectedLanguage ?? employee.defaultLang,
          imagePath: _selectedImage?.path,
        );

        const MainScreen().navigateReplacement;
        context.showBarMessage(context.tr.profileUpdatedSuccessfully);

        ref.invalidate(getProfileFutureProvider);
        ref.invalidate(getHomeFutureProvider);
      } catch (e) {
        context.showBarMessage(context.tr.somethingWentWrong, isError: true);
      }
    }

    final profileModel = profileFuture.when(
      data: (profileData) => profileData,
      loading: () => const ProfileModel(),
      error: (error, stackTrace) => const ProfileModel(),
    );

    final isLoading = profileController.isLoading;

    // Initialize controllers when data is loaded
    if (!isLoading && profileModel.employee.mobile.isNotEmpty) {
      if (_mobileController.text.isEmpty) {
        _mobileController.text = profileModel.employee.mobile;
      }
      _selectedLanguage ??= profileModel.employee.defaultLang;
    }

    return Scaffold(
      backgroundColor: ColorManager.lightGreyBackground,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        title: Text(
          context.tr.settings,
          style: AppTextStyles.title,
        ),
        centerTitle: true,
        leading: IconButton(
          onPressed: () => Navigator.pop(context),
          icon: const Icon(Icons.arrow_back_ios_new),
        ),
        actions: [
          if (!_isEditing)
            IconButton(
              onPressed: () {
                setState(() {
                  _isEditing = true;
                });
              },
              icon: const Icon(Icons.edit),
            ),
          if (_isEditing)
            IconButton(
              onPressed: () {
                setState(() {
                  _isEditing = false;
                  _selectedImage = null;
                  _mobileController.text = profileModel.employee.mobile;
                  _selectedLanguage = profileModel.employee.defaultLang;
                });
              },
              icon: const Icon(Icons.close),
            ),
          IconButton(
            onPressed: () {
              showDialog(
                context: context,
                builder: (_) => const LogoutDialog(),
              );
            },
            icon: const Icon(Icons.logout),
          ),
        ],
      ),
      body: Skeletonizer(
        enabled: isLoading,
        child: Form(
          key: _formKey,
          child: Padding(
            padding: const EdgeInsets.all(AppSpaces.padding16),
            child: Column(
              children: [
                // Profile Picture Section
                Center(
                  child: Stack(
                    children: [
                      _selectedImage != null
                          ? CircleAvatar(
                              radius: 60.r,
                              backgroundImage: FileImage(_selectedImage!),
                              child: _selectedImage == null &&
                                      profileModel.employee.pic.isEmpty
                                  ? Text(
                                      profileModel.employee.name.isNotEmpty
                                          ? profileModel.employee.name
                                              .substring(0, 1)
                                              .toUpperCase()
                                          : 'U',
                                      style: AppTextStyles.title.copyWith(
                                        fontSize: 32,
                                        color: Colors.white,
                                      ),
                                    )
                                  : null,
                            )
                          : profileModel.employee.pic.isNotEmpty
                              ? CircleAvatar(
                                  radius: 60.r,
                                  backgroundImage:
                                      NetworkImage(profileModel.employee.pic),
                                  child: profileModel.employee.pic.isEmpty
                                      ? Text(
                                          profileModel.employee.name.isNotEmpty
                                              ? profileModel.employee.name
                                                  .substring(0, 1)
                                                  .toUpperCase()
                                              : 'U',
                                          style: AppTextStyles.title.copyWith(
                                            fontSize: 32,
                                            color: Colors.white,
                                          ),
                                        )
                                      : null,
                                )
                              : CircleAvatar(
                                  radius: 60.r,
                                  child: Text(
                                    profileModel.employee.name.isNotEmpty
                                        ? profileModel.employee.name
                                            .substring(0, 1)
                                            .toUpperCase()
                                        : 'U',
                                    style: AppTextStyles.title.copyWith(
                                      fontSize: 32,
                                      color: Colors.white,
                                    ),
                                  ),
                                ),
                      // CircleAvatar(
                      //   radius: 60.r,
                      //   backgroundImage: _selectedImage != null
                      //       ? FileImage(_selectedImage!)
                      //       : profileModel.employee.pic.isNotEmpty
                      //           ? NetworkImage(profileModel.employee.pic)
                      //           : null,
                      //   child: _selectedImage == null && profileModel.employee.pic.isEmpty
                      //       ? Text(
                      //           profileModel.employee.name.isNotEmpty
                      //               ? profileModel.employee.name.substring(0, 1).toUpperCase()
                      //               : 'U',
                      //           style: AppTextStyles.title.copyWith(
                      //             fontSize: 32,
                      //             color: Colors.white,
                      //           ),
                      //         )
                      //       : null,
                      // ),
                      if (_isEditing)
                        Positioned(
                          bottom: 0,
                          right: 0,
                          child: GestureDetector(
                            onTap: _pickImage,
                            child: Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: ColorManager.primaryColor,
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: Colors.white,
                                  width: 2,
                                ),
                              ),
                              child: const Icon(
                                Icons.camera_alt,
                                color: Colors.white,
                                size: 20,
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
                AppGaps.gap16,

                // Name
                Text(
                  profileModel.employee.name,
                  style: AppTextStyles.title.copyWith(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                AppGaps.gap24,

                // Settings Container
                Container(
                  padding: const EdgeInsets.all(AppSpaces.padding16),
                  decoration: BoxDecoration(
                    color: Theme.of(context).cardColor,
                    borderRadius: BorderRadius.circular(AppRadius.radius12),
                  ),
                  child: Column(
                    children: [
                      // Email (Read-only)
                      _buildInfoRow(
                        context,
                        icon: Icons.email,
                        title: context.tr.email,
                        value: profileModel.employee.email,
                        isEditable: false,
                      ),
                      AppGaps.gap16,

                      // Mobile Number
                      _isEditing
                          ? TextFormField(
                              controller: _mobileController,
                              decoration: InputDecoration(
                                labelText: context.tr.mobile,
                                prefixIcon: const Icon(Icons.phone),
                                border: OutlineInputBorder(
                                  borderRadius:
                                      BorderRadius.circular(AppRadius.radius8),
                                ),
                              ),
                              keyboardType: TextInputType.phone,
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return context
                                      .tr.responsiblePhoneCannotBeEmpty;
                                }
                                return null;
                              },
                            )
                          : _buildInfoRow(
                              context,
                              icon: Icons.phone,
                              title: context.tr.mobile,
                              value: profileModel.employee.mobile,
                              isEditable: true,
                            ),
                      AppGaps.gap16,

                      // Language
                      _isEditing
                          ? DropdownButtonFormField<String>(
                              value: _selectedLanguage,
                              decoration: InputDecoration(
                                labelText: context.tr.language,
                                prefixIcon: const Icon(Icons.language),
                                border: OutlineInputBorder(
                                  borderRadius:
                                      BorderRadius.circular(AppRadius.radius8),
                                ),
                              ),
                              items: const [
                                DropdownMenuItem(
                                  value: 'en',
                                  child: Text('English'),
                                ),
                                DropdownMenuItem(
                                  value: 'ar',
                                  child: Text('العربية'),
                                ),
                              ],
                              onChanged: (value) {
                                if (value != null) {
                                  setState(() {
                                    _selectedLanguage = value;
                                  });
                                  // Update app language immediately
                                  settingsController
                                      .updateLanguage(Locale(value));
                                }
                              },
                            )
                          : SettingCard(
                              icon: Icons.language,
                              title: context.tr.language,
                              child: Text(
                                settingsController.locale.languageCode
                                    .toUpperCase(),
                                style: AppTextStyles.subTitle,
                              ),
                              // DropdownButton<String>(
                              //   value: settingsController.locale.languageCode,
                              //   alignment: AlignmentDirectional.centerEnd,
                              //   underline: const Center(),
                              //   onChanged: (value) {
                              //     if (value != null) {
                              //       settingsController
                              //           .updateLanguage(Locale(value));
                              //     }
                              //   },
                              //   icon: const Icon(Icons.keyboard_arrow_down),
                              //   items: S.delegate.supportedLocales
                              //       .map((locale) => DropdownMenuItem<String>(
                              //             value: locale.languageCode,
                              //             child: Text(locale.languageCode
                              //                 .toUpperCase()),
                              //           ))
                              //       .toList(),
                              // ),
                            ),
                    ],
                  ),
                ),
                AppGaps.gap32,

                // Update Button (only show when editing)
                if (_isEditing)
                  SizedBox(
                    width: double.infinity,
                    child: Button(
                      isLoading: isLoading,
                      loadingWidget: const LoadingWidget(),
                      onPressed: () => _updateProfile(profileModel.employee),
                      label: context.tr.updateProfile,
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInfoRow(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String value,
    required bool isEditable,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          color: ColorManager.primaryColor,
          size: 24,
        ),
        AppGaps.gap16,
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: AppTextStyles.caption.copyWith(
                  color: ColorManager.lightGrey,
                ),
              ),
              AppGaps.gap4,
              Text(
                value,
                style: AppTextStyles.subTitle.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
