// import 'package:flutter/material.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:hooks_riverpod/hooks_riverpod.dart';
// import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
// import 'package:opti_tickets/src/core/shared/extensions/string_extensions.dart';
// import 'package:opti_tickets/src/core/shared/services/app_settings/controller/settings_controller.dart';
// import 'package:opti_tickets/src/screens/settings/widgets/setting_card.dart';
// import 'package:xr_helper/xr_helper.dart';
//
// import '../../../generated/l10n.dart';
// import '../../core/theme/color_manager.dart';
// import '../home/<USER>/home_model.dart';
// import '../home/<USER>/widgets/home_app_bar_widget.dart';
// import '../profile/view/profile_screen.dart';
//
// class SettingsScreen extends ConsumerWidget {
//   final Employee employee;
//
//   const SettingsScreen({super.key, required this.employee});
//
//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     final settingsController = ref.watch(settingsControllerProvider);
//     final userName = employee.empName
//         .split(' ')
//         .map((word) => word.capitalizeFirstLetter())
//         .join(' ');
//
//     final firstLetterOr2LetterIfExist = userName.isNotEmpty
//         ? userName.length > 1
//             ? userName.substring(0, 2)
//             : userName.substring(0, 1)
//         : '';
//
//     return Scaffold(
//       appBar: AppBar(
//         actions: [
//           IconButton(
//             onPressed: () {
//               showDialog(
//                 context: context,
//                 builder: (_) => const LogoutDialog(),
//               );
//             },
//             icon: Icon(
//               Icons.logout,
//               color: context.isDark ? Colors.white : Colors.black,
//             ),
//           ),
//         ],
//         backgroundColor: Colors.transparent,
//         title: Padding(
//           padding: const EdgeInsets.only(top: AppSpaces.padding4),
//           child: Text(
//             context.tr.settings,
//             style: AppTextStyles.title,
//           ),
//         ),
//         centerTitle: true,
//         leading: IconButton(
//             onPressed: () => context.back(),
//             icon: Icon(
//               Icons.arrow_back_ios_new,
//               color: context.isDark ? Colors.white : Colors.black,
//             )),
//       ),
//       body: Padding(
//         padding: const EdgeInsets.all(AppSpaces.padding16),
//         child: Column(
//           children: [
//             employee.empPIC.isNotEmpty
//                 ? ClipOval(
//                     child: Image.network(
//                       employee.empPIC,
//                       width: 50.r,
//                       height: 50.r,
//                       fit: BoxFit.cover,
//                       errorBuilder: (context, error, stackTrace) {
//                         return Container(
//                           padding: const EdgeInsets.all(AppSpaces.padding8 + 4),
//                           decoration: BoxDecoration(
//                             color: ColorManager.primaryColor
//                                 .withValues(alpha: 0.2),
//                             borderRadius:
//                                 BorderRadius.circular(AppSpaces.padding8),
//                           ),
//                           child: Text(
//                             firstLetterOr2LetterIfExist,
//                             style: AppTextStyles.title.copyWith(
//                               color: ColorManager.primaryColor,
//                               fontSize: 24,
//                             ),
//                           ),
//                         );
//                       },
//                     ),
//                   )
//                 : Container(
//                     padding: const EdgeInsets.all(AppSpaces.padding8 + 4),
//                     decoration: BoxDecoration(
//                       color: ColorManager.primaryColor.withValues(alpha: 0.2),
//                       borderRadius: BorderRadius.circular(AppSpaces.padding8),
//                     ),
//                     child: Text(
//                       firstLetterOr2LetterIfExist,
//                       style: AppTextStyles.title.copyWith(
//                         color: ColorManager.primaryColor,
//                         fontSize: 24,
//                       ),
//                     ),
//                   ),
//             AppGaps.gap16,
//             Text(
//               userName,
//               style: AppTextStyles.title.copyWith(
//                 fontSize: 20,
//                 fontWeight: FontWeight.bold,
//               ),
//             ),
//             AppGaps.gap16,
//             Container(
//               padding: const EdgeInsets.all(AppSpaces.padding8),
//               decoration: BoxDecoration(
//                 color: Theme.of(context).cardColor,
//                 borderRadius: BorderRadius.circular(AppSpaces.padding8),
//               ),
//               child: Column(
//                 children: [
//                   // Profile Setting
//                   SettingCard(
//                     icon: Icons.person,
//                     title: context.tr.profile,
//                     child: IconButton(
//                       onPressed: () {
//                         Navigator.push(
//                           context,
//                           MaterialPageRoute(
//                             builder: (context) => const ProfileScreen(),
//                           ),
//                         );
//                       },
//                       icon: const Icon(Icons.arrow_forward_ios),
//                     ),
//                   ),
//                   AppGaps.gap8,
//                   // Language Setting
//                   SettingCard(
//                       icon: Icons.language,
//                       title: context.tr.language,
//                       child: DropdownButton<String>(
//                           value: settingsController.locale.languageCode,
//                           alignment: AlignmentDirectional.centerEnd,
//                           underline: const Center(),
//                           onChanged: (value) {
//                             if (value != null) {
//                               settingsController.updateLanguage(Locale(value));
//                             }
//                           },
//                           icon: Padding(
//                             padding: EdgeInsets.only(
//                                 bottom:
//                                     context.isDark ? 0 : AppSpaces.padding4),
//                             child: const Icon(Icons.keyboard_arrow_down),
//                           ),
//                           items: S.delegate.supportedLocales
//                               .map((locale) => DropdownMenuItem<String>(
//                                     value: locale.languageCode,
//                                     child: Padding(
//                                       padding: const EdgeInsets.symmetric(
//                                           horizontal: AppSpaces.padding8),
//                                       child: Text(
//                                           locale.languageCode.toUpperCase()),
//                                     ),
//                                   ))
//                               .toList())),
//                 ],
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }
