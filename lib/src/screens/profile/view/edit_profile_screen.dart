import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:image_picker/image_picker.dart';
import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
import 'package:opti_tickets/src/core/theme/color_manager.dart';
import 'package:opti_tickets/src/screens/profile/models/profile_model.dart';
import 'package:opti_tickets/src/screens/profile/providers/profile_providers.dart';
import 'package:xr_helper/xr_helper.dart';

class EditProfileScreen extends ConsumerStatefulWidget {
  final ProfileEmployee employee;

  const EditProfileScreen({
    super.key,
    required this.employee,
  });

  @override
  ConsumerState<EditProfileScreen> createState() => _EditProfileScreenState();
}

class _EditProfileScreenState extends ConsumerState<EditProfileScreen> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _mobileController;
  late String _selectedLanguage;
  File? _selectedImage;
  final ImagePicker _picker = ImagePicker();

  @override
  void initState() {
    super.initState();
    _mobileController = TextEditingController(text: widget.employee.mobile);
    _selectedLanguage = widget.employee.defaultLang;
  }

  @override
  void dispose() {
    _mobileController.dispose();
    super.dispose();
  }

  Future<void> _pickImage() async {
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return SafeArea(
          child: Wrap(
            children: [
              ListTile(
                leading: const Icon(Icons.photo_library),
                title: Text(context.tr.gallery),
                onTap: () async {
                  Navigator.pop(context);
                  final XFile? image = await _picker.pickImage(
                    source: ImageSource.gallery,
                    imageQuality: 80,
                  );
                  if (image != null) {
                    setState(() {
                      _selectedImage = File(image.path);
                    });
                  }
                },
              ),
              ListTile(
                leading: const Icon(Icons.photo_camera),
                title: Text(context.tr.camera),
                onTap: () async {
                  Navigator.pop(context);
                  final XFile? image = await _picker.pickImage(
                    source: ImageSource.camera,
                    imageQuality: 80,
                  );
                  if (image != null) {
                    setState(() {
                      _selectedImage = File(image.path);
                    });
                  }
                },
              ),
            ],
          ),
        );
      },
    );
  }

  Future<void> _updateProfile() async {
    if (!_formKey.currentState!.validate()) return;

    final profileController = ref.read(profileControllerProvider);

    final success = await profileController.updateProfile(
      mobile: _mobileController.text,
      defaultLang: _selectedLanguage,
      imagePath: _selectedImage?.path,
    );

    if (success) {
      if (mounted) {
        context.showBarMessage(context.tr.profileUpdatedSuccessfully);
        Navigator.pop(context);
        // Refresh profile data
        ref.invalidate(getProfileFutureProvider);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorManager.lightGreyBackground,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        title: Text(
          context.tr.editProfile,
          style: AppTextStyles.title,
        ),
        centerTitle: true,
        leading: IconButton(
          onPressed: () => Navigator.pop(context),
          icon: const Icon(Icons.arrow_back_ios_new),
        ),
      ),
      body: Form(
        key: _formKey,
        child: Padding(
          padding: const EdgeInsets.all(AppSpaces.padding16),
          child: Column(
            children: [
              // Profile Picture Section
              Center(
                child: Stack(
                  children: [
                    _selectedImage != null
                        ? CircleAvatar(
                            radius: 60.r,
                            backgroundImage: FileImage(_selectedImage!),
                            child: _selectedImage == null &&
                                    widget.employee.pic.isEmpty
                                ? Text(
                                    widget.employee.name.isNotEmpty
                                        ? widget.employee.name
                                            .substring(0, 1)
                                            .toUpperCase()
                                        : 'U',
                                    style: AppTextStyles.title.copyWith(
                                      fontSize: 32,
                                      color: Colors.white,
                                    ),
                                  )
                                : null,
                          )
                        : widget.employee.pic.isNotEmpty
                            ? CircleAvatar(
                                radius: 60.r,
                                backgroundImage:
                                    NetworkImage(widget.employee.pic),
                                child: widget.employee.pic.isEmpty
                                    ? Text(
                                        widget.employee.name.isNotEmpty
                                            ? widget.employee.name
                                                .substring(0, 1)
                                                .toUpperCase()
                                            : 'U',
                                        style: AppTextStyles.title.copyWith(
                                          fontSize: 32,
                                          color: Colors.white,
                                        ),
                                      )
                                    : null,
                              )
                            : CircleAvatar(
                                radius: 60.r,
                                child: Text(
                                  widget.employee.name.isNotEmpty
                                      ? widget.employee.name
                                          .substring(0, 1)
                                          .toUpperCase()
                                      : '-',
                                  style: AppTextStyles.title.copyWith(
                                    fontSize: 32,
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                    Positioned(
                      bottom: 0,
                      right: 0,
                      child: GestureDetector(
                        onTap: _pickImage,
                        child: Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: ColorManager.primaryColor,
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: Colors.white,
                              width: 2,
                            ),
                          ),
                          child: const Icon(
                            Icons.camera_alt,
                            color: Colors.white,
                            size: 20,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              AppGaps.gap32,

              // Mobile Number Field
              TextFormField(
                controller: _mobileController,
                decoration: InputDecoration(
                  labelText: context.tr.mobile,
                  prefixIcon: const Icon(Icons.phone),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppRadius.radius12),
                  ),
                ),
                keyboardType: TextInputType.phone,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return context.tr.responsiblePhoneCannotBeEmpty;
                  }
                  return null;
                },
              ),
              AppGaps.gap16,

              // Language Selection
              DropdownButtonFormField<String>(
                value: _selectedLanguage,
                decoration: InputDecoration(
                  labelText: context.tr.language,
                  prefixIcon: const Icon(Icons.language),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppRadius.radius12),
                  ),
                ),
                items: const [
                  DropdownMenuItem(
                    value: 'en',
                    child: Text('English'),
                  ),
                  DropdownMenuItem(
                    value: 'ar',
                    child: Text('العربية'),
                  ),
                ],
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedLanguage = value;
                    });
                  }
                },
              ),
              AppGaps.gap32,

              // Update Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _updateProfile,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: ColorManager.primaryColor,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(AppRadius.radius12),
                    ),
                  ),
                  child: Text(
                    context.tr.updateProfile,
                    style: AppTextStyles.subTitle.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
