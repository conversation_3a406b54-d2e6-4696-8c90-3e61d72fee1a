// import 'package:flutter/material.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
// import 'package:opti_tickets/src/core/theme/color_manager.dart';
// import 'package:opti_tickets/src/screens/profile/models/profile_model.dart';
// import 'package:opti_tickets/src/screens/profile/providers/profile_providers.dart';
// import 'package:opti_tickets/src/screens/profile/view/edit_profile_screen.dart';
// import 'package:skeletonizer/skeletonizer.dart';
// import 'package:xr_helper/xr_helper.dart';
//
// class ProfileScreen extends ConsumerWidget {
//   const ProfileScreen({super.key});
//
//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     final profileFuture = ref.watch(getProfileFutureProvider);
//
//     final profileModel = profileFuture.when(
//       data: (profileData) => profileData,
//       loading: () => const ProfileModel(),
//       error: (error, stackTrace) => const ProfileModel(),
//     );
//
//     final isLoading = profileFuture.isLoading;
//
//     return Scaffold(
//       backgroundColor: ColorManager.lightGreyBackground,
//       appBar: AppBar(
//         backgroundColor: Colors.transparent,
//         title: Text(
//           context.tr.profile,
//           style: AppTextStyles.title,
//         ),
//         centerTitle: true,
//         automaticallyImplyLeading: false,
//         actions: [
//           IconButton(
//             onPressed: () {
//               Navigator.push(
//                 context,
//                 MaterialPageRoute(
//                   builder: (context) => EditProfileScreen(
//                     employee: profileModel.employee,
//                   ),
//                 ),
//               );
//             },
//             icon: const Icon(Icons.edit),
//           ),
//         ],
//       ),
//       body: Skeletonizer(
//         enabled: isLoading,
//         child: Padding(
//           padding: const EdgeInsets.all(AppSpaces.padding16),
//           child: Column(
//             children: [
//               // Profile Picture
//               Center(
//                 child: CircleAvatar(
//                   radius: 60.r,
//                   backgroundImage: profileModel.employee.pic.isNotEmpty
//                       ? NetworkImage(profileModel.employee.pic)
//                       : null,
//                   child: profileModel.employee.pic.isEmpty
//                       ? Text(
//                           profileModel.employee.name.isNotEmpty
//                               ? profileModel.employee.name.substring(0, 1).toUpperCase()
//                               : 'U',
//                           style: AppTextStyles.title.copyWith(
//                             fontSize: 32,
//                             color: Colors.white,
//                           ),
//                         )
//                       : null,
//                 ),
//               ),
//               AppGaps.gap16,
//
//               // Name
//               Text(
//                 profileModel.employee.name,
//                 style: AppTextStyles.title.copyWith(
//                   fontSize: 24,
//                   fontWeight: FontWeight.bold,
//                 ),
//               ),
//               AppGaps.gap24,
//
//               // Profile Information Cards
//               _buildInfoCard(
//                 context,
//                 icon: Icons.email,
//                 title: context.tr.email,
//                 value: profileModel.employee.email,
//               ),
//               AppGaps.gap16,
//
//               _buildInfoCard(
//                 context,
//                 icon: Icons.phone,
//                 title: context.tr.mobile,
//                 value: profileModel.employee.mobile,
//               ),
//               AppGaps.gap16,
//
//               _buildInfoCard(
//                 context,
//                 icon: Icons.language,
//                 title: context.tr.language,
//                 value: profileModel.employee.defaultLang.toUpperCase(),
//               ),
//             ],
//           ),
//         ),
//       ),
//     );
//   }
//
//   Widget _buildInfoCard(
//     BuildContext context, {
//     required IconData icon,
//     required String title,
//     required String value,
//   }) {
//     return Container(
//       padding: const EdgeInsets.all(AppSpaces.padding16),
//       decoration: BoxDecoration(
//         color: Theme.of(context).cardColor,
//         borderRadius: BorderRadius.circular(AppRadius.radius12),
//         boxShadow: [
//           BoxShadow(
//             color: Colors.black.withValues(alpha: 0.05),
//             blurRadius: 4,
//             offset: const Offset(0, 2),
//           ),
//         ],
//       ),
//       child: Row(
//         children: [
//           Icon(
//             icon,
//             color: ColorManager.primaryColor,
//             size: 24,
//           ),
//           AppGaps.gap16,
//           Expanded(
//             child: Column(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 Text(
//                   title,
//                   style: AppTextStyles.subTitle.copyWith(
//                     color: ColorManager.lightGrey,
//                   ),
//                 ),
//                 AppGaps.gap4,
//                 Text(
//                   value,
//                   style: AppTextStyles.subTitle.copyWith(
//                     fontWeight: FontWeight.w600,
//                   ),
//                 ),
//               ],
//             ),
//           ),
//         ],
//       ),
//     );
//   }
// }
