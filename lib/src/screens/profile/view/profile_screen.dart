//
// class ProfileScreen extends ConsumerWidget {
//   const ProfileScreen({super.key});
//
//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     final homeFuture = ref.watch(getHomeFutureProvider);
//
//     return Scaffold(
//       backgroundColor: ColorManager.lightGreyBackground,
//       appBar: BaseAppBar(
//         title: context.tr.myProfile,
//       ),
//       body: homeFuture.when(
//         data: (homeData) => _buildProfileContent(context, homeData),
//         loading: () => _buildLoadingContent(context),
//         error: (error, stackTrace) => _buildErrorContent(context),
//       ),
//     );
//   }
//
//   Widget _buildProfileContent(BuildContext context, homeData) {
//     final employee = homeData.employee;
//
//     return SingleChildScrollView(
//       padding: const EdgeInsets.all(AppSpaces.padding16),
//       child: Column(
//         children: [
//           // Profile Picture and Basic Info
//           _buildProfileHeader(context, employee),
//
//           AppGaps.gap32,
//
//           // Profile Information Cards
//           _buildInfoCard(
//             context,
//             icon: Icons.person,
//             title: context.tr.empName,
//             value: employee.empName,
//           ),
//
//           AppGaps.gap16,
//
//           _buildInfoCard(
//             context,
//             icon: Icons.work,
//             title: context.tr.empJob,
//             value: employee.empJob,
//           ),
//
//           AppGaps.gap16,
//
//           _buildInfoCard(
//             context,
//             icon: Icons.access_time,
//             title: context.tr.workTime,
//             value: '${employee.workTimings.start} - ${employee.workTimings.end}',
//           ),
//
//           AppGaps.gap32,
//
//           // Action Buttons
//           _buildActionButtons(context),
//         ],
//       ),
//     );
//   }
//
//   Widget _buildProfileHeader(BuildContext context, employee) {
//     return Column(
//       children: [
//         // Profile Picture
//         Container(
//           width: 120,
//           height: 120,
//           decoration: BoxDecoration(
//             shape: BoxShape.circle,
//             border: Border.all(
//               color: ColorManager.primaryColor,
//               width: 3,
//             ),
//           ),
//           child: ClipOval(
//             child: employee.empPIC != null && employee.empPIC.isNotEmpty
//                 ? Image.network(
//                     employee.empPIC,
//                     fit: BoxFit.cover,
//                     errorBuilder: (context, error, stackTrace) {
//                       return _buildDefaultAvatar();
//                     },
//                   )
//                 : _buildDefaultAvatar(),
//           ),
//         ),
//
//         AppGaps.gap16,
//
//         // Name
//         Text(
//           employee.empName,
//           style: AppTextStyles.title.copyWith(
//             fontSize: 24,
//             fontWeight: FontWeight.bold,
//           ),
//           textAlign: TextAlign.center,
//         ),
//
//         AppGaps.gap8,
//
//         // Job Title
//         Text(
//           employee.empJob,
//           style: AppTextStyles.subTitle.copyWith(
//             color: ColorManager.darkGrey,
//           ),
//           textAlign: TextAlign.center,
//         ),
//       ],
//     );
//   }
//
//   Widget _buildDefaultAvatar() {
//     return Container(
//       color: ColorManager.lightGrey,
//       child: const Icon(
//         Icons.person,
//         size: 60,
//         color: ColorManager.darkGrey,
//       ),
//     );
//   }
//
//   Widget _buildInfoCard(BuildContext context, {
//     required IconData icon,
//     required String title,
//     required String value,
//   }) {
//     return Card(
//       elevation: 2,
//       shape: RoundedRectangleBorder(
//         borderRadius: BorderRadius.circular(AppRadius.radius12),
//       ),
//       child: Padding(
//         padding: const EdgeInsets.all(AppSpaces.padding16),
//         child: Row(
//           children: [
//             Container(
//               padding: const EdgeInsets.all(AppSpaces.padding12),
//               decoration: BoxDecoration(
//                 color: ColorManager.primaryColor.withOpacity(0.1),
//                 borderRadius: BorderRadius.circular(AppRadius.radius8),
//               ),
//               child: Icon(
//                 icon,
//                 color: ColorManager.primaryColor,
//                 size: 24,
//               ),
//             ),
//
//             AppGaps.gap16,
//
//             Expanded(
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   Text(
//                     title,
//                     style: AppTextStyles.labelLarge.copyWith(
//                       color: ColorManager.darkGrey,
//                     ),
//                   ),
//                   AppGaps.gap4,
//                   Text(
//                     value,
//                     style: AppTextStyles.subTitle.copyWith(
//                       fontWeight: FontWeight.w600,
//                     ),
//                   ),
//                 ],
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
//
//   Widget _buildActionButtons(BuildContext context) {
//     return Column(
//       children: [
//         SizedBox(
//           width: double.infinity,
//           child: ElevatedButton.icon(
//             onPressed: () {
//               // Navigate to edit profile
//             },
//             icon: const Icon(Icons.edit),
//             label: Text(context.tr.editProfile),
//             style: ElevatedButton.styleFrom(
//               padding: const EdgeInsets.symmetric(vertical: AppSpaces.padding16),
//               shape: RoundedRectangleBorder(
//                 borderRadius: BorderRadius.circular(AppRadius.radius12),
//               ),
//             ),
//           ),
//         ),
//
//         AppGaps.gap16,
//
//         SizedBox(
//           width: double.infinity,
//           child: OutlinedButton.icon(
//             onPressed: () {
//               // Show logout confirmation
//               _showLogoutDialog(context);
//             },
//             icon: const Icon(Icons.logout),
//             label: Text(context.tr.logout),
//             style: OutlinedButton.styleFrom(
//               padding: const EdgeInsets.symmetric(vertical: AppSpaces.padding16),
//               shape: RoundedRectangleBorder(
//                 borderRadius: BorderRadius.circular(AppRadius.radius12),
//               ),
//             ),
//           ),
//         ),
//       ],
//     );
//   }
//
//   Widget _buildLoadingContent(BuildContext context) {
//     return Skeletonizer(
//       enabled: true,
//       child: SingleChildScrollView(
//         padding: const EdgeInsets.all(AppSpaces.padding16),
//         child: Column(
//           children: [
//             const CircleAvatar(radius: 60),
//             AppGaps.gap16,
//             Container(height: 24, width: 200, color: Colors.grey),
//             AppGaps.gap8,
//             Container(height: 16, width: 150, color: Colors.grey),
//             AppGaps.gap32,
//             ...List.generate(3, (index) => Padding(
//               padding: const EdgeInsets.only(bottom: AppSpaces.padding16),
//               child: Container(height: 80, color: Colors.grey),
//             )),
//           ],
//         ),
//       ),
//     );
//   }
//
//   Widget _buildErrorContent(BuildContext context) {
//     return Center(
//       child: Column(
//         mainAxisAlignment: MainAxisAlignment.center,
//         children: [
//           const Icon(
//             Icons.error_outline,
//             size: 64,
//             color: ColorManager.errorColor,
//           ),
//           AppGaps.gap16,
//           Text(
//             context.tr.somethingWentWrong,
//             style: AppTextStyles.subTitle,
//           ),
//           AppGaps.gap16,
//           ElevatedButton(
//             onPressed: () {
//               // Retry loading
//             },
//             child: Text(context.tr.retry),
//           ),
//         ],
//       ),
//     );
//   }
//
//   void _showLogoutDialog(BuildContext context) {
//     showDialog(
//       context: context,
//       builder: (BuildContext context) {
//         return AlertDialog(
//           title: Text(context.tr.logout),
//           content: Text(context.tr.areYouSureYouWantToLogout),
//           actions: [
//             TextButton(
//               onPressed: () => Navigator.of(context).pop(),
//               child: Text(context.tr.cancel),
//             ),
//             ElevatedButton(
//               onPressed: () {
//                 Navigator.of(context).pop();
//                 // Perform logout
//               },
//               child: Text(context.tr.logout),
//             ),
//           ],
//         );
//       },
//     );
//   }
// }
