import 'package:opti_tickets/src/core/consts/network/api_endpoints.dart';
import 'package:opti_tickets/src/screens/profile/models/profile_model.dart';
import 'package:xr_helper/xr_helper.dart';

class ProfileRepository with BaseRepository {
  final BaseApiServices networkApiService;

  ProfileRepository({
    required this.networkApiService,
  });

  // * Get Profile
  Future<ProfileModel> getProfile() async {
    return baseFunction(
      () async {
        const url = ApiEndpoints.profile;

        final response = await networkApiService.getResponse(url);

        if (response == null ||
            response['dt'] == null ||
            response['dt'].isEmpty) {
          return ProfileModel.empty();
        }

        final profileModel = ProfileModel.fromJson(response);

        return profileModel;
      },
    );
  }

  // * Update Profile
  Future<bool> updateProfile({
    required String mobile,
    required String defaultLang,
    String? imagePath,
  }) async {
    return baseFunction(
      () async {
        const url = ApiEndpoints.profileUpdate;

        final body = <String, dynamic>{
          'mobile': mobile,
          'default_lang': defaultLang,
        };

        final filePaths = imagePath != null ? [imagePath] : <String>[];

        final response = await networkApiService.postResponse(
          url,
          body: body,
          fieldName: 'pic',
          filePaths: filePaths,
        );

        return response['success'] == true;
      },
    );
  }
}
