import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../core/shared/providers/network_api_service_provider.dart';
import '../controllers/profile_controller.dart';
import '../repositories/profile_repository.dart';

// * Profile Repo Provider ========================================
final profileRepoProvider = Provider<ProfileRepository>((ref) {
  final networkApiService = ref.watch(networkServiceProvider);

  return ProfileRepository(networkApiService: networkApiService);
});

// * Profile Controller Provider ========================================
final profileControllerProvider = Provider<ProfileController>((ref) {
  final profileRepo = ref.watch(profileRepoProvider);

  return ProfileController(
    profileRepo: profileRepo,
  );
});

// change notifier provider
final profileControllerNotifierProvider =
    ChangeNotifierProvider<ProfileController>(
  (ref) {
    final profileRepo = ref.watch(profileRepoProvider);

    return ProfileController(
      profileRepo: profileRepo,
    );
  },
);

// * Get Profile Future Provider ========================================
final getProfileFutureProvider = FutureProvider((ref) {
  final profileRepo = ref.watch(profileRepoProvider);

  return profileRepo.getProfile();
});
