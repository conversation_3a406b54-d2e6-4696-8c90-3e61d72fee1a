import 'package:equatable/equatable.dart';

class ProfileModel extends Equatable {
  final ProfileEmployee employee;

  const ProfileModel({
    this.employee = const ProfileEmployee(),
  });

  factory ProfileModel.fromJson(Map<String, dynamic> json) {
    final dt = json['dt'] ?? {};
    return ProfileModel(
      employee: ProfileEmployee.fromJson(dt['employee'] ?? {}),
    );
  }

  factory ProfileModel.empty() => const ProfileModel();

  @override
  List<Object?> get props => [employee];
}

class ProfileEmployee extends Equatable {
  final String pic;
  final String name;
  final String email;
  final String mobile;
  final String defaultLang;

  const ProfileEmployee({
    this.pic = '',
    this.name = '',
    this.email = '',
    this.mobile = '',
    this.defaultLang = 'en',
  });

  factory ProfileEmployee.fromJson(Map<String, dynamic> json) {
    return ProfileEmployee(
      pic: json['pic'] ?? '',
      name: json['name'] ?? '',
      email: json['email'] ?? '',
      mobile: json['mobile'] ?? '',
      defaultLang: json['default_lang'] ?? 'en',
    );
  }

  ProfileEmployee copyWith({
    String? pic,
    String? name,
    String? email,
    String? mobile,
    String? defaultLang,
  }) {
    return ProfileEmployee(
      pic: pic ?? this.pic,
      name: name ?? this.name,
      email: email ?? this.email,
      mobile: mobile ?? this.mobile,
      defaultLang: defaultLang ?? this.defaultLang,
    );
  }

  @override
  List<Object?> get props => [pic, name, email, mobile, defaultLang];
}
