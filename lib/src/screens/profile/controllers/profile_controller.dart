import 'package:opti_tickets/src/screens/profile/models/profile_model.dart';
import 'package:opti_tickets/src/screens/profile/repositories/profile_repository.dart';
import 'package:xr_helper/xr_helper.dart';

class ProfileController extends BaseVM {
  final ProfileRepository profileRepo;

  ProfileController({
    required this.profileRepo,
  });

  // * Get Profile
  Future<ProfileModel> getProfile() async {
    return await baseFunction(
      () async {
        return await profileRepo.getProfile();
      },
    );
  }

  // * Update Profile
  Future<bool> updateProfile({
    required String mobile,
    required String defaultLang,
    String? imagePath,
  }) async {
    return await baseFunction(
      () async {
        return await profileRepo.updateProfile(
          mobile: mobile,
          defaultLang: defaultLang,
          imagePath: imagePath,
        );
      },
    );
  }
}
