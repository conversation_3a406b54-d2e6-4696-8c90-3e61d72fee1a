import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
import 'package:opti_tickets/src/core/theme/color_manager.dart';
import 'package:opti_tickets/src/screens/clients/models/client_model.dart';
import 'package:opti_tickets/src/screens/clients/providers/client_providers.dart';
import 'package:xr_helper/xr_helper.dart';

class AddQuotationStepsScreen extends HookConsumerWidget {
  const AddQuotationStepsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentStep = useState(0);
    final selectedClient = useState<Client?>(null);
    final selectedProducts = useState<List<dynamic>>([]);
    final selectedSubProducts = useState<List<dynamic>>([]);

    const totalSteps = 3;

    return Scaffold(
      backgroundColor: ColorManager.lightGreyBackground,
      appBar: AppBar(
        title: Text(context.tr.saveQuotation),
        backgroundColor: ColorManager.primaryColor,
        foregroundColor: Colors.white,
        leading: IconButton(
          onPressed: () => Navigator.pop(context),
          icon: const Icon(Icons.arrow_back_ios_new),
        ),
      ),
      body: Column(
        children: [
          // Step Indicator
          _buildStepIndicator(context, currentStep.value, totalSteps),

          // Step Content
          Expanded(
            child: _buildStepContent(
              context,
              ref,
              currentStep.value,
              selectedClient,
              selectedProducts,
              selectedSubProducts,
            ),
          ),

          // Navigation Buttons
          _buildNavigationButtons(
            context,
            currentStep,
            totalSteps,
            selectedClient,
            selectedProducts,
            selectedSubProducts,
          ),
        ],
      ),
    );
  }

  Widget _buildStepIndicator(
      BuildContext context, int currentStep, int totalSteps) {
    return Container(
      padding: const EdgeInsets.all(AppSpaces.padding16),
      color: Colors.white,
      child: Row(
        children: List.generate(totalSteps, (index) {
          final isActive = index <= currentStep;
          final isCompleted = index < currentStep;

          return Expanded(
            child: Row(
              children: [
                Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: isActive
                        ? ColorManager.primaryColor
                        : ColorManager.lightGrey,
                  ),
                  child: Center(
                    child: isCompleted
                        ? const Icon(
                            Icons.check,
                            color: Colors.white,
                            size: 16,
                          )
                        : Text(
                            '${index + 1}',
                            style: AppTextStyles.labelLarge.copyWith(
                              color: isActive
                                  ? Colors.white
                                  : ColorManager.darkGrey,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                  ),
                ),
                if (index < totalSteps - 1)
                  Expanded(
                    child: Container(
                      height: 2,
                      margin: const EdgeInsets.symmetric(
                          horizontal: AppSpaces.padding8),
                      color: isCompleted
                          ? ColorManager.primaryColor
                          : ColorManager.lightGrey,
                    ),
                  ),
              ],
            ),
          );
        }),
      ),
    );
  }

  Widget _buildStepContent(
    BuildContext context,
    WidgetRef ref,
    int currentStep,
    ValueNotifier<Client?> selectedClient,
    ValueNotifier<List<dynamic>> selectedProducts,
    ValueNotifier<List<dynamic>> selectedSubProducts,
  ) {
    switch (currentStep) {
      case 0:
        return _buildClientSelectionStep(context, ref, selectedClient);
      case 1:
        return _buildProductSelectionStep(context, ref, selectedProducts);
      case 2:
        return _buildSubProductSelectionStep(context, ref, selectedSubProducts);
      default:
        return const SizedBox.shrink();
    }
  }

  Widget _buildClientSelectionStep(
    BuildContext context,
    WidgetRef ref,
    ValueNotifier<Client?> selectedClient,
  ) {
    final clientsAsyncValue = ref.watch(getClientListFutureProvider);

    return Padding(
      padding: const EdgeInsets.all(AppSpaces.padding16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '${context.tr.step} 1 ${context.tr.of1} 3',
            style: AppTextStyles.labelLarge.copyWith(
              color: ColorManager.darkGrey,
            ),
          ),
          AppGaps.gap8,
          Text(
            context.tr.clientSelection,
            style: AppTextStyles.title.copyWith(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          AppGaps.gap24,
          clientsAsyncValue.when(
            data: (clientModel) {
              final clients = clientModel.clients;

              if (clients.isEmpty) {
                return Center(
                  child: Column(
                    children: [
                      const Icon(
                        Icons.people_outline,
                        size: 64,
                        color: ColorManager.lightGrey,
                      ),
                      AppGaps.gap16,
                      Text(
                        context.tr.noDataFound,
                        style: AppTextStyles.body.copyWith(
                          color: ColorManager.lightGrey,
                        ),
                      ),
                    ],
                  ),
                );
              }

              return Expanded(
                child: ListView.separated(
                  itemCount: clients.length,
                  separatorBuilder: (context, index) => AppGaps.gap12,
                  itemBuilder: (context, index) {
                    final client = clients[index];
                    final isSelected =
                        selectedClient.value?.clientID == client.clientID;

                    return Card(
                      elevation: isSelected ? 4 : 1,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(AppRadius.radius12),
                        side: BorderSide(
                          color: isSelected
                              ? ColorManager.primaryColor
                              : Colors.transparent,
                          width: 2,
                        ),
                      ),
                      child: ListTile(
                        onTap: () {
                          selectedClient.value = client;
                        },
                        leading: CircleAvatar(
                          backgroundColor: isSelected
                              ? ColorManager.primaryColor
                              : ColorManager.lightGrey,
                          child: Icon(
                            Icons.business,
                            color: isSelected
                                ? Colors.white
                                : ColorManager.darkGrey,
                          ),
                        ),
                        title: Text(
                          client.clientname,
                          style: AppTextStyles.subTitle.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        subtitle: Text(
                          client.clientstatus,
                          style: AppTextStyles.labelLarge.copyWith(
                            color: ColorManager.darkGrey,
                          ),
                        ),
                        trailing: isSelected
                            ? const Icon(
                                Icons.check_circle,
                                color: ColorManager.primaryColor,
                              )
                            : null,
                      ),
                    );
                  },
                ),
              );
            },
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (error, stackTrace) => Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.error_outline,
                    size: 64,
                    color: ColorManager.errorColor,
                  ),
                  AppGaps.gap16,
                  Text(
                    context.tr.somethingWentWrong,
                    style: AppTextStyles.subTitle,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProductSelectionStep(
    BuildContext context,
    WidgetRef ref,
    ValueNotifier<List<dynamic>> selectedProducts,
  ) {
    return Padding(
      padding: const EdgeInsets.all(AppSpaces.padding16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '${context.tr.step} 2 ${context.tr.of1} 3',
            style: AppTextStyles.labelLarge.copyWith(
              color: ColorManager.darkGrey,
            ),
          ),
          AppGaps.gap8,
          Text(
            context.tr.productSelection,
            style: AppTextStyles.title.copyWith(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          AppGaps.gap24,

          // Product selection will be implemented here
          const Expanded(
            child: Center(
              child: Text('Product selection coming soon...'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSubProductSelectionStep(
    BuildContext context,
    WidgetRef ref,
    ValueNotifier<List<dynamic>> selectedSubProducts,
  ) {
    return Padding(
      padding: const EdgeInsets.all(AppSpaces.padding16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '${context.tr.step} 3 ${context.tr.of1} 3',
            style: AppTextStyles.labelLarge.copyWith(
              color: ColorManager.darkGrey,
            ),
          ),
          AppGaps.gap8,
          Text(
            context.tr.subProductSelection,
            style: AppTextStyles.title.copyWith(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          AppGaps.gap24,

          // Sub-product selection will be implemented here
          const Expanded(
            child: Center(
              child: Text('Sub-product selection coming soon...'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNavigationButtons(
    BuildContext context,
    ValueNotifier<int> currentStep,
    int totalSteps,
    ValueNotifier<Client?> selectedClient,
    ValueNotifier<List<dynamic>> selectedProducts,
    ValueNotifier<List<dynamic>> selectedSubProducts,
  ) {
    return Container(
      padding: const EdgeInsets.all(AppSpaces.padding16),
      color: Colors.white,
      child: Row(
        children: [
          if (currentStep.value > 0)
            Expanded(
              child: OutlinedButton(
                onPressed: () {
                  currentStep.value--;
                },
                child: Text(context.tr.previous),
              ),
            ),
          if (currentStep.value > 0) AppGaps.gap16,
          Expanded(
            child: ElevatedButton(
              onPressed: () {
                if (currentStep.value < totalSteps - 1) {
                  // Validate current step
                  if (_canProceedToNextStep(currentStep.value, selectedClient,
                      selectedProducts, selectedSubProducts)) {
                    currentStep.value++;
                  } else {
                    // Show validation message
                    _showValidationMessage(context, currentStep.value);
                  }
                } else {
                  // Final step - save quotation
                  _saveQuotation(context, selectedClient, selectedProducts,
                      selectedSubProducts);
                }
              },
              child: Text(
                currentStep.value < totalSteps - 1
                    ? context.tr.next
                    : context.tr.finish,
              ),
            ),
          ),
        ],
      ),
    );
  }

  bool _canProceedToNextStep(
    int currentStep,
    ValueNotifier<Client?> selectedClient,
    ValueNotifier<List<dynamic>> selectedProducts,
    ValueNotifier<List<dynamic>> selectedSubProducts,
  ) {
    switch (currentStep) {
      case 0:
        return selectedClient.value != null;
      case 1:
        return selectedProducts.value.isNotEmpty;
      case 2:
        return selectedSubProducts.value.isNotEmpty;
      default:
        return false;
    }
  }

  void _showValidationMessage(BuildContext context, int currentStep) {
    String message;
    switch (currentStep) {
      case 0:
        message = context.tr.pleaseSelectClientFirst;
        break;
      case 1:
        message = context.tr.pleaseSelectAtLeastOneProduct;
        break;
      case 2:
        message = context.tr.pleaseSelectAtLeastOneSubProduct;
        break;
      default:
        message = context.tr.somethingWentWrong;
    }

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: ColorManager.errorColor,
      ),
    );
  }

  void _saveQuotation(
    BuildContext context,
    ValueNotifier<Client?> selectedClient,
    ValueNotifier<List<dynamic>> selectedProducts,
    ValueNotifier<List<dynamic>> selectedSubProducts,
  ) {
    // Implementation for saving quotation
    Navigator.pop(context);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(context.tr.quotationSaved),
        backgroundColor: Colors.green,
      ),
    );
  }
}
