import 'dart:convert';
import 'dart:developer';

import 'package:http/http.dart' as http;
import 'package:opti_tickets/src/core/consts/network/api_endpoints.dart';
import 'package:opti_tickets/src/screens/quotations/models/quotation_model.dart';
import 'package:xr_helper/xr_helper.dart';

class QuotationRepository with BaseRepository {
  final BaseApiServices networkApiService;

  QuotationRepository({
    required this.networkApiService,
  });

  // * Get Quotation List
  Future<QuotationListModel> getQuotationList() async {
    return baseFunction(
      () async {
        const url = ApiEndpoints.quotationsList;

        final response = await networkApiService.getResponse(url);

        if (response == null) {
          return QuotationListModel.empty();
        }

        final quotationListModel = QuotationListModel.fromJson(response);

        return quotationListModel;
      },
    );
  }

  // * Add Quotation
  Future<bool> addQuotation({
    required AddQuotationModel quotationData,
  }) async {
    return baseFunction(
      () async {
        const url = ApiEndpoints.quotationSave;

        final res = await http.post(Uri.parse(url),
            body: jsonEncode(
              quotationData.toJson(),
            ),
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
              'Authorization':
                  "Bearer ${GetStorageService.getData(key: LocalKeys.token)}",
            });

        log('asfasfasf ${res.body} fwfw ${res.statusCode}');

        return true;
      },
    );
  }
}
