import 'package:opti_tickets/src/screens/quotations/models/quotation_model.dart';
import 'package:opti_tickets/src/screens/quotations/repositories/quotation_repository.dart';
import 'package:xr_helper/xr_helper.dart';

class QuotationController extends BaseVM {
  final QuotationRepository quotationRepo;

  QuotationController({
    required this.quotationRepo,
  });

  // * Get Quotation List
  Future<QuotationListModel> getQuotationList() async {
    return await baseFunction(
      () async {
        return await quotationRepo.getQuotationList();
      },
    );
  }

  // * Add Quotation
  Future<bool> addQuotation({
    required AddQuotationModel quotationData,
  }) async {
    return await baseFunction(
      () async {
        return await quotationRepo.addQuotation(
          quotationData: quotationData,
        );
      },
    );
  }
}
