import 'package:equatable/equatable.dart';

class HomeModel extends Equatable {
  final Employee employee;
  final Attendance attendance;
  final CurrentMonthStats currentMonthStats;
  final Tasks tasks;
  final Tickets tickets;
  final Contracts contracts;
  final List<Contract> contractsAboutExpire;

  const HomeModel({
    this.employee = const Employee(),
    this.attendance = const Attendance(),
    this.currentMonthStats = const CurrentMonthStats(),
    this.tasks = const Tasks(),
    this.tickets = const Tickets(),
    this.contracts = const Contracts(),
    this.contractsAboutExpire = const [],
  });

  // * From Json
  factory HomeModel.fromJson(Map<String, dynamic> json) {
    final dt = json['dt'] ?? {};
    return HomeModel(
      employee: Employee.from<PERSON>son(dt['employee'] ?? {}),
      attendance: Attendance.fromJson(dt['attendance'] ?? {}),
      currentMonthStats:
          CurrentMonthStats.fromJson(dt['current_month_stats'] ?? {}),
      tasks: Tasks.from<PERSON>son(dt['tasks'] ?? {}),
      tickets: Tickets.fromJson(dt['tickets'] ?? {}),
      contracts: Contracts.fromJson(dt['contracts'] ?? {}),
      contractsAboutExpire: (dt['contracts_about_expire'] as List<dynamic>?)
              ?.map((e) => Contract.fromJson(e))
              .toList() ??
          [],
    );
  }

  factory HomeModel.empty() => const HomeModel();

  @override
  List<Object?> get props => [
        employee,
        attendance,
        currentMonthStats,
        tasks,
        tickets,
        contracts,
        contractsAboutExpire
      ];
}

class Employee extends Equatable {
  final String empPIC;
  final String empName;
  final String empJob;
  final WorkTimings workTimings;

  const Employee({
    this.empPIC = '',
    this.empName = '',
    this.empJob = '',
    this.workTimings = const WorkTimings(),
  });

  factory Employee.fromJson(Map<String, dynamic> json) {
    return Employee(
      empPIC: json['empPIC'] ?? '',
      empName: json['empName'] ?? '',
      empJob: json['empJob'] ?? '',
      workTimings: WorkTimings.fromJson(json['work_timings'] ?? {}),
    );
  }

  @override
  List<Object?> get props => [empPIC, empName, empJob, workTimings];
}

class WorkTimings extends Equatable {
  final String? start;
  final String? end;

  const WorkTimings({
    this.start,
    this.end,
  });

  factory WorkTimings.fromJson(Map<String, dynamic> json) {
    return WorkTimings(
      start: json['start'],
      end: json['end'],
    );
  }

  @override
  List<Object?> get props => [start, end];
}

class Attendance extends Equatable {
  final bool allowAttend;
  final String? attend;
  final String? checkout;

  const Attendance({
    this.allowAttend = false,
    this.attend,
    this.checkout,
  });

  factory Attendance.fromJson(Map<String, dynamic> json) {
    return Attendance(
      allowAttend: json['allow_attend'] ?? false,
      attend: json['attend'],
      checkout: json['checkout'],
    );
  }

  @override
  List<Object?> get props => [allowAttend, attend, checkout];
}

class CurrentMonthStats extends Equatable {
  final int totalCompleteAttends;
  final int totalIncompleteAttends;
  final int totalAbsents;
  final int totalOfficialHolidays;
  final int totalVacationLeaves;
  final int totalRequestLeaves;
  final int totalSickLeaves;

  const CurrentMonthStats({
    this.totalCompleteAttends = 0,
    this.totalIncompleteAttends = 0,
    this.totalAbsents = 0,
    this.totalOfficialHolidays = 0,
    this.totalVacationLeaves = 0,
    this.totalRequestLeaves = 0,
    this.totalSickLeaves = 0,
  });

  factory CurrentMonthStats.fromJson(Map<String, dynamic> json) {
    return CurrentMonthStats(
      totalCompleteAttends: json['total_complete_attends'] ?? 0,
      totalIncompleteAttends: json['total_incomplete_attends'] ?? 0,
      totalAbsents: json['total_absents'] ?? 0,
      totalOfficialHolidays: json['total_official_holidays'] ?? 0,
      totalVacationLeaves: json['total_vacation_leaves'] ?? 0,
      totalRequestLeaves: json['total_request_leaves'] ?? 0,
      totalSickLeaves: json['total_sick_leaves'] ?? 0,
    );
  }

  int get total =>
      totalCompleteAttends +
      totalIncompleteAttends +
      totalAbsents +
      totalOfficialHolidays +
      totalVacationLeaves +
      totalRequestLeaves +
      totalSickLeaves;

  @override
  List<Object?> get props => [
        totalCompleteAttends,
        totalIncompleteAttends,
        totalAbsents,
        totalOfficialHolidays,
        totalVacationLeaves,
        totalRequestLeaves,
        totalSickLeaves,
      ];
}

class Tasks extends Equatable {
  final int active;
  final int finished;

  const Tasks({
    this.active = 0,
    this.finished = 0,
  });

  factory Tasks.fromJson(Map<String, dynamic> json) {
    return Tasks(
      active: json['active'] ?? 0,
      finished: json['finished'] ?? 0,
    );
  }

  @override
  List<Object?> get props => [active, finished];
}

class Tickets extends Equatable {
  final int active;
  final int finished;

  const Tickets({
    this.active = 0,
    this.finished = 0,
  });

  factory Tickets.fromJson(Map<String, dynamic> json) {
    return Tickets(
      active: json['active'] ?? 0,
      finished: json['finished'] ?? 0,
    );
  }

  @override
  List<Object?> get props => [active, finished];
}

class Contracts extends Equatable {
  final int nearExpire;
  final int isExpired;

  const Contracts({
    this.nearExpire = 0,
    this.isExpired = 0,
  });

  factory Contracts.fromJson(Map<String, dynamic> json) {
    return Contracts(
      nearExpire: json['near_expire'] ?? 0,
      isExpired: json['is_expired'] ?? 0,
    );
  }

  @override
  List<Object?> get props => [nearExpire, isExpired];
}

class Contract extends Equatable {
  final String code;
  final String type;
  final String client;
  final String endDate;
  final int remainingDays;

  const Contract({
    this.code = '',
    this.type = '',
    this.client = '',
    this.endDate = '',
    this.remainingDays = 0,
  });

  factory Contract.fromJson(Map<String, dynamic> json) {
    return Contract(
      code: json['code'] ?? '',
      type: json['type'] ?? '',
      client: json['client'] ?? '',
      endDate: json['end_date'] ?? '',
      remainingDays: json['remaining_days'] ?? 0,
    );
  }

  @override
  List<Object?> get props => [code, type, client, endDate, remainingDays];
}

const demoHomeModel = HomeModel(
  employee: Employee(
    empPIC: "http://app.iv4it.com/assets/users/alaabahi.jpg",
    empName: "علاء باهي",
    empJob: "دعم فني",
    workTimings: WorkTimings(start: "9:00 AM", end: "5:00 PM"),
  ),
  attendance: Attendance(
    allowAttend: true,
    attend: "10:15",
    checkout: null,
  ),
  currentMonthStats: CurrentMonthStats(
    totalCompleteAttends: 1,
    totalIncompleteAttends: 1,
    totalAbsents: 14,
    totalOfficialHolidays: 0,
    totalVacationLeaves: 0,
    totalRequestLeaves: 0,
    totalSickLeaves: 0,
  ),
  tasks: Tasks(
    active: 0,
    finished: 2,
  ),
  tickets: Tickets(
    active: 0,
    finished: 2,
  ),
  contracts: Contracts(
    nearExpire: 0,
    isExpired: 7,
  ),
  contractsAboutExpire: [
    Contract(
      code: "MB\\RUH\\24\\CC003",
      type: "CC",
      client: "مؤسسة عائض الظافر للتجارة",
      endDate: "2025-06-30",
      remainingDays: 11,
    ),
  ],
);
