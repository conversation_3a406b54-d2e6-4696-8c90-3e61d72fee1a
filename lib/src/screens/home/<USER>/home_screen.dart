import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
import 'package:opti_tickets/src/core/theme/color_manager.dart';
import 'package:opti_tickets/src/screens/home/<USER>/home_model.dart';
import 'package:opti_tickets/src/screens/home/<USER>/home_providers.dart';
import 'package:opti_tickets/src/screens/home/<USER>/widgets/attendance_card_widget.dart';
import 'package:opti_tickets/src/screens/home/<USER>/widgets/contracts_expiration_slider.dart';
import 'package:opti_tickets/src/screens/home/<USER>/widgets/home_app_bar_widget.dart';
import 'package:opti_tickets/src/screens/home/<USER>/widgets/stats_card_widget.dart';
import 'package:opti_tickets/src/screens/quotations/view/quotations_screen.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:xr_helper/xr_helper.dart';

class HomeScreen extends ConsumerWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final homeFuture = ref.watch(getHomeFutureProvider);

    final homeModel = homeFuture.when(
      data: (homeData) => homeData,
      loading: () => const HomeModel(),
      error: (error, stackTrace) => const HomeModel(),
    );

    final isLoading = homeFuture.isLoading;
    final homeData = isLoading ? demoHomeModel : homeModel;

    return Scaffold(
      backgroundColor: ColorManager.lightGreyBackground,
      body: CustomScrollView(
        slivers: [
          SliverAppBar(
            automaticallyImplyLeading: false,
            pinned: true,
            expandedHeight: 110.h,
            shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(AppRadius.radius24),
                bottomRight: Radius.circular(AppRadius.radius24),
              ),
            ),
            flexibleSpace: FlexibleSpaceBar(
              background: Skeletonizer(
                enabled: isLoading,
                child: HomeAppBarWidget(
                  employee: homeData.employee,
                ),
              ),
            ),
          ),
          const SliverToBoxAdapter(
            child: AppGaps.gap16,
          ),
          SliverToBoxAdapter(
            child: Skeletonizer(
              enabled: isLoading,
              child: AttendanceCardWidget(
                attendance: homeData.attendance,
                workTimings: homeData.employee.workTimings,
              ),
            ),
          ),
          const SliverToBoxAdapter(
            child: AppGaps.gap16,
          ),
          SliverToBoxAdapter(
            child: Skeletonizer(
              enabled: isLoading,
              child: ContractsExpirationSlider(
                contracts: homeData.contractsAboutExpire,
              ),
            ),
          ),
          const SliverToBoxAdapter(
            child: AppGaps.gap16,
          ),
          SliverToBoxAdapter(
            child: Skeletonizer(
              enabled: isLoading,
              child: StatsCardWidget(
                stats: homeData.currentMonthStats,
              ),
            ),
          ),
          const SliverToBoxAdapter(
            child: AppGaps.gap16,
          ),
          SliverToBoxAdapter(
            child: _buildAgreementsSection(context),
          ),
          const SliverToBoxAdapter(
            child: AppGaps.gap16,
          ),
        ],
      ),
    );
  }

  Widget _buildAgreementsSection(BuildContext context) {
    final List<Map<String, dynamic>> agreementItems = [
      {
        'icon': Icons.description,
        'title': context.tr.quotations,
        'onTap': () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const QuotationsScreen(),
            ),
          );
        },
      },
      {
        'icon': Icons.card_membership,
        'title': context.tr.licenses,
        'onTap': () {
          // Will be implemented later
        },
      },
      {
        'icon': Icons.build,
        'title': context.tr.maintenance,
        'onTap': () {
          // Will be implemented later
        },
      },
      {
        'icon': Icons.help_center,
        'title': context.tr.helpCenter,
        'onTap': () {
          // Will be implemented later
        },
      },
      {
        'icon': Icons.settings,
        'title': context.tr.softwareManagement,
        'onTap': () {
          // Will be implemented later
        },
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: AppSpaces.padding16),
          child: Text(
            context.tr.agreements,
            style: AppTextStyles.title.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        AppGaps.gap16,
        SizedBox(
          height: 120,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding:
                const EdgeInsets.symmetric(horizontal: AppSpaces.padding16),
            itemCount: agreementItems.length,
            itemBuilder: (context, index) {
              final item = agreementItems[index];
              return Container(
                margin: EdgeInsets.only(
                  right: index == agreementItems.length - 1
                      ? 0
                      : AppSpaces.padding16,
                ),
                child: _buildCircleAvatar(
                  context,
                  icon: item['icon'],
                  title: item['title'],
                  onTap: item['onTap'],
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildCircleAvatar(
    BuildContext context, {
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          CircleAvatar(
            radius: 30,
            backgroundColor: ColorManager.primaryColor,
            child: Icon(
              icon,
              color: Colors.white,
              size: 24,
            ),
          ),
          AppGaps.gap8,
          SizedBox(
            width: 80,
            child: Text(
              title,
              textAlign: TextAlign.center,
              style: AppTextStyles.labelSmall.copyWith(
                fontWeight: FontWeight.w500,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }
}
