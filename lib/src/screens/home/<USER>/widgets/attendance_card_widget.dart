import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
import 'package:opti_tickets/src/core/theme/color_manager.dart';
import 'package:opti_tickets/src/screens/home/<USER>/home_model.dart';
import 'package:opti_tickets/src/screens/home/<USER>/home_providers.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../core/services/local_auth_service.dart';

class AttendanceCardWidget extends ConsumerWidget {
  final Attendance attendance;
  final WorkTimings workTimings;

  const AttendanceCardWidget({
    super.key,
    required this.attendance,
    required this.workTimings,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final homeController = ref.watch(homeControllerNotifierProvider);

    // Determine current attendance time to display
    String displayTime = '--:--';
    if (attendance.attend != null) {
      displayTime = attendance.attend!;
    }

    if (attendance.checkout != null) {
      displayTime = '${attendance.attend} - ${attendance.checkout}';
    }

    // Determine work time display
    String workTimeDisplay = context.tr.workTime;
    if (workTimings.start != null && workTimings.end != null) {
      workTimeDisplay =
          '${context.tr.workTime}: ${workTimings.start} - ${workTimings.end}';
    }

    // Determine button state
    bool isCheckIn = attendance.attend == null;

    String buttonLabel = isCheckIn ? context.tr.checkIn : context.tr.checkOut;
    final isCheckedInAndCheckedOut =
        attendance.attend != null && attendance.checkout != null;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: AppSpaces.screenPadding),
      padding: const EdgeInsets.all(AppSpaces.padding20),
      decoration: BoxDecoration(
        color: ColorManager.white,
        borderRadius: BorderRadius.circular(AppRadius.radius16),
        boxShadow: [
          BoxShadow(
            color: ColorManager.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            displayTime,
            style: AppTextStyles.title.copyWith(
              fontSize: 25.sp,
              fontWeight: FontWeight.bold,
              color: ColorManager.primaryColor,
            ),
            textDirection: TextDirection.rtl,
          ),
          AppGaps.gap16,
          Text(
            workTimeDisplay,
            style: AppTextStyles.title.copyWith(
              fontSize: 14.sp,
              color: ColorManager.secondaryColor,
            ),
          ),
          AppGaps.gap8,
          if (attendance.allowAttend && !isCheckedInAndCheckedOut) ...[
            AppGaps.gap8,

            // Divider
            Container(
              height: 1,
              color: ColorManager.lightGrey,
            ),

            AppGaps.gap16,

            Button(
              color: isCheckIn ? ColorManager.primaryColor : ColorManager.red,
              onPressed: attendance.allowAttend
                  ? () async {
                      try {
                        // Check if biometric authentication is available
                        bool isBiometricAvailable =
                            await LocalAuthService.isBiometricAvailable();

                        if (!isBiometricAvailable) {
                          if (context.mounted) {
                              context.showBarMessage(context.tr.biometricNotAvailable,
                                isError: true);
                          }
                          return;
                        }

                        // Authenticate with fingerprint
                        String authReason = isCheckIn
                            ? context.tr.authenticateToCheckIn
                            : context.tr.authenticateToCheckOut;

                        bool isAuthenticated =
                            await LocalAuthService.authenticateWithBiometrics(
                          localizedReason: authReason,
                          biometricOnly: true,
                        );

                        if (!isAuthenticated) {
                          if (context.mounted) {
                            context.showBarMessage(
                                context.tr.authenticationFailed,
                                isError: true);
                          }
                          return;
                        }

                        // Proceed with attendance submission after successful authentication
                        String type = isCheckIn ? 'attend' : 'leave';
                        bool success =
                            await homeController.submitAttendance(type);

                        if (success) {
                          // Refresh home data
                          ref.invalidate(getHomeFutureProvider);

                          if (context.mounted) {
                            context.showBarMessage(
                              isCheckIn
                                  ? context.tr.checkInSuccessful
                                  : context.tr.checkOutSuccessful,
                            );
                          }
                        } else {
                          if (context.mounted) {
                            context.showBarMessage(
                              isCheckIn
                                  ? context.tr.checkInFailed
                                  : context.tr.checkOutFailed,
                            );
                          }
                        }
                      } catch (e) {
                        if (context.mounted) {
                          context.showBarMessage(
                              context.tr.authenticationFailed,
                              isError: true);
                        }
                      }
                    }
                  : null,
              label: buttonLabel,
              isPrefixIcon: true,
              icon: const Icon(
                Icons.fingerprint,
                color: ColorManager.white,
                size: 26,
              ),
            )
          ]
        ],
      ),
    );
  }
}
