import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
import 'package:opti_tickets/src/core/theme/color_manager.dart';
import 'package:opti_tickets/src/screens/home/<USER>/home_model.dart';
import 'package:xr_helper/xr_helper.dart';

class ContractsExpirationSlider extends StatelessWidget {
  final List<Contract> contracts;

  const ContractsExpirationSlider({
    super.key,
    required this.contracts,
  });

  @override
  Widget build(BuildContext context) {
    if (contracts.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding:
              const EdgeInsets.symmetric(horizontal: AppSpaces.screenPadding),
          child: Text(
            context.tr.contractsExpiring,
            style: AppTextStyles.title.copyWith(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: ColorManager.black,
            ),
          ),
        ),
        AppGaps.gap12,
        SizedBox(
          height: 105.h,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding:
                const EdgeInsets.symmetric(horizontal: AppSpaces.screenPadding),
            itemCount: contracts.length,
            itemBuilder: (context, index) {
              final contract = contracts[index];
              return Container(
                width: 280.w,
                margin: EdgeInsets.only(
                  right:
                      index == contracts.length - 1 ? 0 : AppSpaces.padding16,
                ),
                padding: const EdgeInsets.all(AppSpaces.padding16),
                decoration: BoxDecoration(
                  color: ColorManager.white,
                  borderRadius: BorderRadius.circular(AppRadius.radius16),
                  border: Border.all(
                    color: contract.remainingDays <= 7
                        ? ColorManager.red
                        : contract.remainingDays <= 30
                            ? ColorManager.orange
                            : ColorManager.primaryColor,
                    width: 2,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: ColorManager.black.withOpacity(0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Contract type and remaining days
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: AppSpaces.padding8,
                            vertical: AppSpaces.padding4,
                          ),
                          decoration: BoxDecoration(
                            color: ColorManager.primaryColor.withOpacity(0.1),
                            borderRadius:
                                BorderRadius.circular(AppRadius.radius8),
                          ),
                          child: Text(
                            contract.code,
                            style: AppTextStyles.labelMedium.copyWith(
                              color: ColorManager.primaryColor,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: AppSpaces.padding8,
                            vertical: AppSpaces.padding4,
                          ),
                          decoration: BoxDecoration(
                            color: contract.remainingDays <= 7
                                ? ColorManager.red.withOpacity(0.1)
                                : contract.remainingDays <= 30
                                    ? ColorManager.orange.withOpacity(0.1)
                                    : ColorManager.green.withOpacity(0.1),
                            borderRadius:
                                BorderRadius.circular(AppRadius.radius8),
                          ),
                          child: Text(
                            '${contract.remainingDays} ${context.tr.days}',
                            style: AppTextStyles.labelSmall.copyWith(
                              color: contract.remainingDays <= 7
                                  ? ColorManager.red
                                  : contract.remainingDays <= 30
                                      ? ColorManager.orange
                                      : ColorManager.green,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const Spacer(),
                    Text(
                      '${context.tr.expiryDate}: ${contract.endDate}',
                      style: AppTextStyles.labelSmall.copyWith(
                        color: ColorManager.darkBlue,
                      ),
                    ),
                    const Spacer(),
                    Text(
                      '${context.tr.clientName}: ${contract.client}',
                      style: AppTextStyles.labelSmall.copyWith(
                        color: ColorManager.darkBlue,
                        overflow: TextOverflow.ellipsis,
                      ),
                      maxLines: 1,
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}
