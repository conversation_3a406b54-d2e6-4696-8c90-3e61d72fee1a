import 'package:opti_tickets/src/screens/clients/models/client_model.dart';
import 'package:opti_tickets/src/screens/clients/repositories/client_repository.dart';
import 'package:xr_helper/xr_helper.dart';

class ClientController extends BaseVM {
  final ClientRepository clientRepo;

  ClientController({
    required this.clientRepo,
  });

  // * Get Client List
  Future<ClientListModel> getClientList() async {
    return await baseFunction(
      () async {
        return await clientRepo.getClientList();
      },
    );
  }

  // * Add Client
  Future<bool> addClient({
    required AddClientModel clientData,
  }) async {
    return await baseFunction(
      () async {
        return await clientRepo.addClient(
          clientData: clientData,
        );
      },
    );
  }
}
