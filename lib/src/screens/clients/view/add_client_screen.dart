import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
import 'package:opti_tickets/src/core/shared/widgets/loading/loading_widget.dart';
import 'package:opti_tickets/src/core/theme/color_manager.dart';
import 'package:opti_tickets/src/screens/clients/models/client_model.dart';
import 'package:opti_tickets/src/screens/clients/providers/client_providers.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../core/shared/widgets/fields/text_field.dart';

class AddClientScreen extends ConsumerStatefulWidget {
  const AddClientScreen({super.key});

  @override
  ConsumerState<AddClientScreen> createState() => _AddClientScreenState();
}

class _AddClientScreenState extends ConsumerState<AddClientScreen> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _companyNameController;
  late TextEditingController _companyRegistrationController;
  late TextEditingController _responsibleNameController;
  late TextEditingController _responsiblePhoneController;
  late TextEditingController _responsibleJobController;
  late TextEditingController _responsibleEmailController;

  late DateTime _startTime;
  late String _startDate;
  late String _startTimeString;

  @override
  void initState() {
    super.initState();
    _companyNameController = TextEditingController();
    _companyRegistrationController = TextEditingController();
    _responsibleNameController = TextEditingController();
    _responsiblePhoneController = TextEditingController();
    _responsibleJobController = TextEditingController();
    _responsibleEmailController = TextEditingController();

    // Record the start time when screen opens
    _startTime = DateTime.now();
    _startDate = DateFormat('yyyy-MM-dd', 'en').format(_startTime);
    _startTimeString = DateFormat('HH:mm:ss', 'en').format(_startTime);
  }

  @override
  void dispose() {
    _companyNameController.dispose();
    _companyRegistrationController.dispose();
    _responsibleNameController.dispose();
    _responsiblePhoneController.dispose();
    _responsibleJobController.dispose();
    _responsibleEmailController.dispose();
    super.dispose();
  }

  Future<void> _submitClient() async {
    if (!_formKey.currentState!.validate()) return;

    // Record finish time
    final finishTime = DateTime.now();
    final finishDate = DateFormat('yyyy-MM-dd', 'en').format(finishTime);
    final finishTimeString = DateFormat('HH:mm:ss', 'en').format(finishTime);

    final clientData = AddClientModel(
      clName: _companyNameController.text,
      clCr: _companyRegistrationController.text,
      clResponsibleName: _responsibleNameController.text,
      clMobile: _responsiblePhoneController.text,
      clResponsibleJob: _responsibleJobController.text,
      clEmail: _responsibleEmailController.text,
      task: ClientTask(
        startDate: _startDate,
        startTime: _startTimeString,
        finishDate: finishDate,
        finishTime: finishTimeString,
      ),
    );

    final clientController = ref.read(clientControllerProvider);

    final success = await clientController.addClient(clientData: clientData);

    if (success && mounted) {
      ref.invalidate(getClientListFutureProvider);
      Navigator.pop(context);
      context.showBarMessage(context.tr.clientAdded);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorManager.lightGreyBackground,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        title: Text(
          context.tr.addClient,
          style: AppTextStyles.title,
        ),
        centerTitle: true,
        leading: IconButton(
          onPressed: () => Navigator.pop(context),
          icon: const Icon(Icons.arrow_back_ios_new),
        ),
      ),
      body: Form(
        key: _formKey,
        child: Padding(
          padding: const EdgeInsets.all(AppSpaces.padding16),
          child: SingleChildScrollView(
            child: Column(
              children: [
                // Company Name
                BaseTextField(
                  name: 'companyName',
                  controller: _companyNameController,
                  title: context.tr.companyName,
                  hint: context.tr.enter + ' ' + context.tr.companyName,
                  icon: const Icon(Icons.business),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return context.tr.companyNameCannotBeEmpty;
                    }
                    return null;
                  },
                ),
                AppGaps.gap16,

                // Company Registration
                BaseTextField(
                  name: 'companyRegistration',
                  controller: _companyRegistrationController,
                  title: context.tr.companyRegistration,
                  hint: context.tr.enter + ' ' + context.tr.companyRegistration,
                  icon: const Icon(Icons.assignment),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Company registration cannot be empty';
                    }
                    return null;
                  },
                ),
                AppGaps.gap16,

                // Responsible Name
                BaseTextField(
                  name: 'responsibleName',
                  controller: _responsibleNameController,
                  title: context.tr.responsibleName,
                  hint: context.tr.enter + ' ' + context.tr.responsibleName,
                  icon: const Icon(Icons.person),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return context.tr.responsibleNameCannotBeEmpty;
                    }
                    return null;
                  },
                ),
                AppGaps.gap16,

                // Responsible Phone
                BaseTextField(
                  name: 'responsiblePhone',
                  controller: _responsiblePhoneController,
                  title: context.tr.responsiblePhone,
                  hint: context.tr.enter + ' ' + context.tr.responsiblePhone,
                  icon: const Icon(Icons.phone),
                  textInputType: TextInputType.phone,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return context.tr.responsiblePhoneCannotBeEmpty;
                    }
                    return null;
                  },
                ),
                AppGaps.gap16,

                // Responsible Job
                BaseTextField(
                  name: 'responsibleJob',
                  controller: _responsibleJobController,
                  title: context.tr.responsibleJob,
                  hint: context.tr.enter + ' ' + context.tr.responsibleJob,
                  icon: const Icon(Icons.work),
                  isRequired: false,
                ),
                AppGaps.gap16,

                // Responsible Email
                BaseTextField(
                  name: 'responsibleEmail',
                  controller: _responsibleEmailController,
                  title: context.tr.responsibleEmail,
                  hint: context.tr.enter + ' ' + context.tr.responsibleEmail,
                  icon: const Icon(Icons.email),
                  textInputType: TextInputType.emailAddress,
                  isRequired: false,
                ),
                AppGaps.gap48,

                // Submit Button
                SizedBox(
                  width: double.infinity,
                  child: Button(
                      isLoading:
                          ref.watch(clientControllerNotifierProvider).isLoading,
                      loadingWidget: const LoadingWidget(),
                      onPressed: _submitClient,
                      label: context.tr.submit),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
