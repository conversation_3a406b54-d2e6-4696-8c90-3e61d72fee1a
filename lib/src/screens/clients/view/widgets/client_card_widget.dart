import 'package:flutter/material.dart';
import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
import 'package:opti_tickets/src/core/theme/color_manager.dart';
import 'package:opti_tickets/src/screens/clients/models/client_model.dart';
import 'package:xr_helper/xr_helper.dart';

class ClientCardWidget extends StatelessWidget {
  final Client client;

  const ClientCardWidget({
    super.key,
    required this.client,
  });

  @override
  Widget build(BuildContext context) {
    final statusColor = client.isActive
        ? ColorManager.primaryColor
        : ColorManager.secondaryColor;

    final statusText = client.isActive ? context.tr.active : context.tr.leads;

    return Container(
      padding: const EdgeInsets.all(AppSpaces.padding16),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(AppRadius.radius12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with status
          Row(
            children: [
              Expanded(
                child: Text(
                  client.clientname,
                  style: AppTextStyles.subTitle.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppSpaces.padding8,
                  vertical: AppSpaces.padding4,
                ),
                decoration: BoxDecoration(
                  color: statusColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(AppRadius.radius8),
                ),
                child: Text(
                  statusText,
                  style: AppTextStyles.caption.copyWith(
                      color: statusColor,
                      fontWeight: FontWeight.w600,
                      fontSize: 14),
                ),
              ),
            ],
          ),
          AppGaps.gap8,

          // Client ID
          Row(
            children: [
              Icon(
                Icons.tag,
                size: 16,
                color: ColorManager.lightGrey,
              ),
              AppGaps.gap8,
              Text(
                'ID: ${client.clientID}',
                style: AppTextStyles.caption.copyWith(
                  color: ColorManager.lightGrey,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
