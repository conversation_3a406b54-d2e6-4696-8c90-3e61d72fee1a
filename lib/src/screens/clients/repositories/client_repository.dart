import 'package:opti_tickets/src/core/consts/network/api_endpoints.dart';
import 'package:opti_tickets/src/screens/clients/models/client_model.dart';
import 'package:xr_helper/xr_helper.dart';

class ClientRepository with BaseRepository {
  final BaseApiServices networkApiService;

  ClientRepository({
    required this.networkApiService,
  });

  // * Get Client List
  Future<ClientListModel> getClientList() async {
    return baseFunction(
      () async {
        const url = ApiEndpoints.clientsList;

        final response = await networkApiService.getResponse(url);

        if (response == null) {
          return ClientListModel.empty();
        }

        final clientListModel = ClientListModel.fromJson(response);

        return clientListModel;
      },
    );
  }

  // * Add Client
  Future<bool> addClient({
    required AddClientModel clientData,
  }) async {
    return baseFunction(
      () async {
        const url = ApiEndpoints.clientAdd;

        final body = clientData.toJson();

        final response = await networkApiService.postResponse(
          url,
          body: body,
        );

        return response['success'] == true;
      },
    );
  }
}
