import 'package:flutter/material.dart';

class ColorManager {
  static const primaryColor = Color(0xFF00569E);
  // static const primaryColor = Color(0xFF4DA1B0);
  static const darkerPrimaryColor = Color(0xFF3A7A85);
  static final lightPrimaryColor = const Color(0xFF4DA1B0).withOpacity(0.6);

  static const secondaryColor = Color(0xFF000000);
  // static const secondaryColor = Color(0xFFF56B3F);

  static const buttonColor = secondaryColor;
  static final backgroundColor = Colors.grey.withOpacity(0.1);
  static final containerColor = Colors.grey.withOpacity(0.1);
  static const selectedContainerColor = Color(0xFFD3E1E2);
  static const fieldColor = Color(0xFFCBD5E1);
  static const white = Color(0xFFFFFFFF);
  static const black = Color(0xFF000000);
  static const grey = Color(0xFFf5f5f5);
  static const greyIcon = Color(0xFF9E9E9E);
  static const highlightColor = Color(0xFFFFFFFF);
  static const lightGrey = Color(0xFFC8C9CB);
  static const lightGreyBackground = Color(0xFFF5F5F5);

  static const shimmerBaseColor = Color(0xFFCECECE);
  static const cardColor = Color(0xFFEDEDED);
  static const darkGrey = Color(0xFFA4A4A4);
  static const darkBlue = Color(0xFF23292F);
  static const iconColor = Color(0xFF727272);
  static const errorColor = Color(0xFFE74C3C);
  static const successColor = Color(0xFF2ECC71);
  static const red = Color(0xFFE74C3C);
  static const orange = Color(0xFFF39C12);
  static const green = Color(0xFF2ECC71);
}
