import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';

import '../../../theme/color_manager.dart';
import 'controller/bottom_nav_bar.controller.dart';

class BottomNavBarWidget extends ConsumerWidget {
  const BottomNavBarWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedIndex = ref.watch(bottomNavigationControllerProvider);

    return BottomNavigationBar(
      currentIndex: selectedIndex,
      type: BottomNavigationBarType.fixed,
      selectedItemColor: ColorManager.primaryColor,
      unselectedItemColor: ColorManager.darkGrey,
      backgroundColor: Colors.white,
      elevation: 8,
      items: [
        BottomNavigationBarItem(
          icon: Icon(
            CupertinoIcons.home,
            color: selectedIndex == 0
                ? ColorManager.primaryColor
                : ColorManager.darkGrey,
          ),
          label: context.tr.home,
        ),
        BottomNavigationBarItem(
          icon: Icon(
            FontAwesomeIcons.calendarDays,
            color: selectedIndex == 1
                ? ColorManager.primaryColor
                : ColorManager.darkGrey,
          ),
          label: context.tr.leaves,
        ),
        BottomNavigationBarItem(
          icon: Icon(
            FontAwesomeIcons.users,
            color: selectedIndex == 2
                ? ColorManager.primaryColor
                : ColorManager.darkGrey,
          ),
          label: context.tr.clients,
        ),
      ],
      onTap: (index) {
        ref
            .read(bottomNavigationControllerProvider.notifier)
            .changeIndex(index);
      },
    );
  }
}
