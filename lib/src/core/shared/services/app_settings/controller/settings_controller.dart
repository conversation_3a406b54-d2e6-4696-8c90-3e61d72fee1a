import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../repository/local_settings_repo.dart';

final settingsControllerProvider =
    ChangeNotifierProvider<AppSettingsController>((ref) {
  final settingsLocalRepo = ref.watch(settingsRepoProvider);
  return AppSettingsController(settingsLocalRepo: settingsLocalRepo)
    ..loadSettings();
});

class AppSettingsController extends BaseVM {
  final SettingsLocalRepo settingsLocalRepo;

  AppSettingsController({required this.settingsLocalRepo});

  //! Load Settings ===================================
  Future<void> loadSettings() async {
    _locale = await settingsLocalRepo.locale();
    _themeMode = await settingsLocalRepo.themeMode();

    notifyListeners();
  }

  Locale _locale = const Locale('en', 'US');
  ThemeMode _themeMode = ThemeMode.light;
  // ThemeMode.system;

  Locale get locale => _locale;
  ThemeMode get themeMode => _themeMode;

  bool get isEnglish => _locale.languageCode == 'en';

  //! Update Language  ===================================
  Future<void> updateLanguage(Locale? newLocale) async {
    if (newLocale == null || _locale == newLocale) return;
    _locale = newLocale;
    await settingsLocalRepo.updateLanguage(newLocale);
    notifyListeners();
  }

  //! Update Theme Mode  ===================================
  Future<void> updateThemeMode(ThemeMode? newThemeMode) async {
    if (newThemeMode == null || _themeMode == newThemeMode) return;
    _themeMode = newThemeMode;
    await settingsLocalRepo.updateThemeMode(newThemeMode);
    notifyListeners();
  }
}
